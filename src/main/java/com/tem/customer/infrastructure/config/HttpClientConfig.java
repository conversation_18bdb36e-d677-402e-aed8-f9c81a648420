package com.tem.customer.infrastructure.config;

import com.iplatform.common.utils.LogUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.DefaultConnectionKeepAliveStrategy;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestClient;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * HTTP客户端配置
 * 提供统一的RestClient配置，支持各种HTTP请求场景
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Configuration
public class HttpClientConfig {

    /**
     * 配置Apache HttpClient连接池
     * 使用HttpClient 5推荐的PoolingHttpClientConnectionManagerBuilder
     *
     * @return CloseableHttpClient实例
     */
    @Bean
    public CloseableHttpClient httpClient() {
        // 使用新的Builder模式，无需手动创建Registry
        PoolingHttpClientConnectionManager poolingConnectionManager =
            PoolingHttpClientConnectionManagerBuilder.create()
                .setDefaultSocketConfig(SocketConfig.custom()
                    .setSoTimeout(Timeout.ofSeconds(5))
                    .build())
                .setDefaultConnectionConfig(ConnectionConfig.custom()
                    .setConnectTimeout(Timeout.ofSeconds(5))
                    .build())
                .setMaxConnTotal(500)
                .setMaxConnPerRoute(50)
                .build();

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionKeepAlive(TimeValue.ofSeconds(20))
                .setConnectionRequestTimeout(Timeout.ofSeconds(5))
                .setResponseTimeout(Timeout.ofSeconds(30))
                .build();

        return HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(poolingConnectionManager)
                .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                .build();
    }

    /**
     * HTTP请求响应日志拦截器
     * 用于记录HTTP请求和响应的详细信息
     */
    @Slf4j
    static class HttpLoggingInterceptor implements ClientHttpRequestInterceptor {

        @Override
        @NonNull
        public ClientHttpResponse intercept(HttpRequest request, byte @NonNull [] body,
                                          @NonNull ClientHttpRequestExecution execution) throws IOException {

            if (request.getMethod().equals(HttpMethod.POST) || request.getMethod().equals(HttpMethod.PUT)) {
                String requestBody = new String(body, StandardCharsets.UTF_8);
                if (!requestBody.trim().isEmpty()) {
                    // 限制日志长度，避免过长的请求体影响日志可读性
                    String logBody = requestBody.length() > 1000 ?
                            requestBody.substring(0, 1000) + "...[truncated]" : requestBody;
                    LogUtils.info(log, "请求体: {}", logBody);
                }
            }

            // 执行请求
            ClientHttpResponse response = execution.execute(request, body);
            ClientHttpResponse responseWrapper = new BufferingClientHttpResponseWrapper(response);

            // 记录响应信息
            LogUtils.info(log, "响应状态: {}", responseWrapper.getStatusCode());

            HttpHeaders headers = response.getHeaders();
            String contentType = headers.getContentType() != null ? headers.getContentType().toString() : "";

            String responseBody = StreamUtils.copyToString(responseWrapper.getBody(), StandardCharsets.UTF_8);
            if (contentType.contains("application/json") || contentType.contains("text/plain")) {
                // 限制日志长度，避免过长的响应体影响日志可读性
                String logBody = responseBody.length() > 1000 ?
                        responseBody.substring(0, 1000) + "...[truncated]" : responseBody;
                LogUtils.info(log, "响应体: {}", logBody);
            } else {
                LogUtils.info(log, "非JSON/文本响应，Content-Type: {}", contentType);
            }

            return responseWrapper;
        }
    }

    /**
     * 支持多次读取的HTTP响应包装器
     */
    static class BufferingClientHttpResponseWrapper implements ClientHttpResponse {

        private final ClientHttpResponse response;
        private byte[] body;

        BufferingClientHttpResponseWrapper(ClientHttpResponse response) {
            this.response = response;
        }

        @NonNull
        @Override
        public org.springframework.http.HttpStatusCode getStatusCode() throws IOException {
            return this.response.getStatusCode();
        }

        @Override
        @NonNull
        public String getStatusText() throws IOException {
            return this.response.getStatusText();
        }

        @Override
        @NonNull
        public HttpHeaders getHeaders() {
            return this.response.getHeaders();
        }

        @Override
        @NonNull
        public InputStream getBody() throws IOException {
            if (this.body == null) {
                this.body = StreamUtils.copyToByteArray(this.response.getBody());
            }
            return new ByteArrayInputStream(this.body);
        }

        @Override
        public void close() {
            this.response.close();
        }
    }

    /**
     * 通用RestClient配置
     * 提供统一的HTTP客户端，支持各种业务场景
     *
     * @param restClientBuilder Spring Boot提供的RestClient.Builder
     * @return RestClient实例
     */
    @Bean
    @Primary
    public RestClient restClient(RestClient.Builder restClientBuilder) {
        // 使用自定义的Apache HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClient());

        RestClient restClient = restClientBuilder
                .requestFactory(requestFactory)
                // 添加HTTP日志拦截器
                .requestInterceptor(new HttpLoggingInterceptor())
                // 配置消息转换器
                .messageConverters(converters -> {
                    // 设置字符串转换器的默认编码为UTF-8
                    converters.stream()
                            .filter(converter -> converter instanceof StringHttpMessageConverter)
                            .forEach(converter -> ((StringHttpMessageConverter) converter)
                                    .setDefaultCharset(StandardCharsets.UTF_8));
                })
                // 配置默认请求头
                .defaultHeaders(headers -> {
                    headers.set("User-Agent", "Customer-Admin-Web/1.0");
                    headers.set("Accept-Charset", "UTF-8");
                })
                .build();

        log.info("RestClient配置完成 - 连接池最大连接数: 500, 每路由最大连接数: 50");
        log.info("HTTP客户端类型: Apache HttpClient 5.x with Connection Pool");
        log.info("HTTP请求响应日志拦截器已启用");

        return restClient;
    }
}
